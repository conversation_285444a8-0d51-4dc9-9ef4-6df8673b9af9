MEMORY PROFILING
================
FLASH Use

----------

MODULE                                Use[Byte]
===============================================
Dtc                                       25386
gtm_eisb                                  23230
Diagcanmgm_Ferrari                        22348
DiagMgm                                   20242
GTM_HostInterface                         16728
Rli                                        9198
ee                                         7281
main                                       7256
Mathlib                                    7058
CanMgmOut_BR                               6626
TLE9278BQX_Mgm                             6012
gtm_atom                                   5976
TLE9278BQX_Com                             5408
CoilAngPattern                             5368
Adc                                        5138
tpe                                        5018
ccp                                        4872
Mcan                                       4598
IonAcqBufRec                               4480
gtm_tim                                    4260
IonKnockSpikeDet                           4146
Task_Isr_VecTable_c2                       4110
Task_Isr_VecTable_c0                       4110
TLE9278BQX_Get                             3956
PwrMgm                                     3950
KnockCorrAdp                               3890
IonPhaseMgm                                3744
CoilTarget                                 3700
recovery                                   3402
CanMgmIn_BR                                3248
IonDwellMgm                                3158
KnockCorrTot                               3090
RonDetectCnt                               3070
IonAcqCircMgm                              3006
IONChargeCtrl                              2958
IonIntMgm                                  2952
RonDetectEst                               2868
msparkcmd                                  2868
crank_isb                                  2760
eemgm                                      2756
CombAvgFFS                                 2596
TTcan                                      2538
TLE9278BQX_Diag                            2464
CombAdp                                    2402
Flash                                      2378
AnalogIn                                   2330
IonKnockPower                              2322
Active_Diag                                2312
port                                       2282
gtm_tom                                    2212
KnockCorrNom                               2124
IonKnockInt                                2124
Task_Isr_PriTable                          2048
gtm_mcs                                    1900
Mcan_events                                1848
buckdiagmgm                                1784
RonDetectMgm                               1782
TempECUMgm                                 1781
MKnockDet                                  1720
IonKnockFFT                                1690
SyncMgm                                    1614
ignincmd                                   1484
TLE9278BQX_Cfg                             1462
isb                                        1392
RonDetectEn                                1388
libarch                                    1326
WDT                                        1304
IVOR_c0_handlers_GHS                       1266
IVOR_c2_handlers_GHS                       1266
IonKnockEn                                 1238
ionacq                                     1208
KnockCorrMgm                               1206
CombBal                                    1190
dspi                                       1180
WDT_wrapper                                1176
ivor_c0                                    1132
RonDetectFuel                              1126
RonDetectCross                             1116
OS_tasks                                   1102
crank_event                                1096
CanMgm                                     1094
mpc5500_user_init                          1028
task                                        990
gtm_cmu                                     968
TLE9278BQX_Prs                              952
dma                                         948
gtm_psm                                     940
IonKnockAirCorr                             888
IonMisf                                     862
IonKnockState                               846
timing                                      846
livenessMgm                                 840
MisfThrMgm                                  818
IonAcqParEval                               816
OS_api                                      704
Diagcanmgm                                  620
gtm_dpll                                    616
start_z4_GHS                                578
Pit_events                                  568
CoilTimPattern                              562
gtm_brc                                     556
RamCheckSM_MCU_4_xx                         526
FlashCheckSM_MCU_r_xx                       516
CombTotCorr                                 498
IGNLoadTest                                 462
DigIn                                       458
clock                                       446
mpc5500_asmcfg_mmu_GHS                      440
TLE9278BQX_IvorEE                           428
flashinit                                   428
TasksDefs                                   416
setlock                                     410
getlock                                     394
intsrcmgm                                   384
libstartup                                  374
OS_Resources                                358
Flashmgm                                    358
entrypoint                                  356
Pit                                         338
loadmgm                                     336
utils                                       320
TbKnockAdEE_mgm                             316
gtm_tbu                                     314
gtm_eisb_Interface                          308
TbInjCorrAdEE_mgm                           300
SafetyMngr                                  282
ivor_c2                                     268
flashsuspend                                252
ccp_can_interface                           250
OS_alarms                                   234
Vsram_shared_IO                             232
dma_events                                  216
TLE9278BQX_IOs                              214
gtm                                         208
stm_events                                  202
flashresume                                 188
main_c2                                     166
stm                                         164
TempMgm                                     160
gtm_icm                                     158
vsram                                       158
syncmgm_EISB_BR_16C                         128
vsrammgm                                    124
recmgm                                      122
SafetyMngr_INTC                             120
SafetyMngr_PIT                              112
OS_Hook                                     104
ccptxdata                                   102
Digio                                        72
app_tag                                      64
calib_tag                                    64
fft_lib                                      32
sys                                          32
IonAcqBufMgm                                 31
TTcan_events                                 22
Cfg_SkipVal_wrapper                          20
gtm_aru                                      18
asr_s32                                      18
Cfg_UpdateVal_wrapper                        18
fc_EECntSBCResend_SetVal_wrapper               16
calib_checkVersion                           16
fc_Diag_SetVal_wrapper                       16
FlashCheckSM_MCU_patternrww0                 16
FlashCheckSM_MCU_patternrww1                 16
Ret_SBCData_Addr_wrapper                     10
get_app_startup                               4
app_checkVersion                              4
Diag_Return_Addr_U8_wrapper                   4
Cfg_Return_Addr_U16_wrapper                   4
EECntSBCResend_Addr_U16_wrapper                4
stub                                          2
------------------------------------------------
total                                    363325
=================================================================
PERCENTAGE of FLASH USED                    17%    (TOTAL FLASH USED / FLASH SIZE)*100
-----------------------------------------------------------------


RAM Use

----------

MODULE                                Use[Byte]
===============================================
gtm_eisb                                  11991
IonAcqBufMgm                               9419
tpe                                        8268
ccp                                        5630
ee_ID9                                     4792
IonAcqBufRec                               4332
fft_lib                                    3426
isb_cfg                                    2116
recmgm                                     1790
recmgm_calib                               1768
DiagMgm_calib                              1744
CoilAngPattern                             1662
IONChargeCtrl                              1490
dspi                                       1462
CoilTarget                                 1452
IonDwellMgm                                1430
Dtc_calib                                  1152
ee_ID8                                     1099
IonKnockFFT                                1040
flashinit                                  1004
ee_ID0                                      996
IonKnockPower                               994
Adc                                         962
gtm_tim                                     924
flashcheckstatus                            872
OS_api                                      849
IonPhaseMgm                                 829
ee_ID3                                      712
DiagMgm                                     693
gtm_tim_cfg                                 672
crank_event                                 664
MisfThrMgm                                  638
IonKnockInt                                 622
TLE9278BQX_Mgm                              599
flash_asynch                                580
IonKnockSpikeDet                            579
CombAvgFFS                                  530
SafetyMngr                                  519
flasherase                                  460
msparkcmd                                   429
IonIntMgm                                   406
RonDetectEst                                382
task                                        358
RonDetectCnt                                347
gtm_mcs_cfg                                 336
flashprogram                                320
KnockCorrTot                                300
KnockCorrAdp                                290
CombAdp                                     280
RonDetectEn                                 274
IonKnockEn                                  273
Flash                                       262
Mcan                                        254
ignincmd                                    235
TLE9278BQX_Cfg                              232
ee                                          232
dma_events                                  220
Active_Diag                                 212
TempECUMgm                                  212
AnalogIn_calib                              200
main                                        198
programverify                               192
CombBal                                     191
IonKnockAirCorr                             187
TLE9278BQX_Prs                              184
IonAcqCircMgm                               181
CanMgmIn_BR_calib                           180
ee_ID6                                      168
blankcheck                                  164
Flash_asynchCbk                             163
gtm_psm_cfg                                 160
ee_ID2                                      156
crank_isb                                   152
AnalogIn                                    150
stub                                        142
KnockCorrNom                                141
KnockCorrMgm                                136
gtm_atom_cfg                                136
TLE9278BQX_Get                              136
CanMgmIn_BR                                 130
MKnockDet                                   126
gtm_dpll_cfg                                108
OS_tasks                                    104
CombTotCorr                                 102
IonKnockState                               100
CanMgmOut_BR                                100
PwrMgm                                       96
CanMgmOut_BR_calib                           96
vsram_shared_content                         96
gtm_brc_cfg                                  96
ionacq                                       93
ee_ID1                                       92
WDT                                          89
IonAcqParEval                                88
GTM_HostInterface                            86
gtm_mcs                                      84
TLE9278BQX_Diag                              80
TLE9278BQX_Com                               74
TTcan                                        72
gtm_tom_cfg                                  64
Mcan_events                                  64
gtm_atom                                     64
RonDetectMgm                                 62
ivor_c2                                      59
SyncMgm                                      55
ee_ID7                                       54
buckdiagmgm_calib                            52
ivor_c0                                      51
DIAGCANMGM_calib                             48
Flashmgm                                     45
CoilTimPattern                               45
vsram_content                                43
RonDetectFuel                                41
utils                                        40
IonMisf                                      36
msparkcmd_calib                              35
TSparkCtrlAdat                               34
ignincmd_calib                               32
livenessMgm                                  28
Diagcanmgm_Ferrari                           26
timing                                       25
P2NoiseDetect                                24
gtm                                          24
gtm_psm                                      24
gtm_cmu                                      20
CanMgm                                       20
TagInca_calib                                20
Diagcanmgm                                   18
ccp_can_interface                            18
ee_ID5                                       17
ee_ID4                                       17
IGNLoadTest                                  17
SparkPlugTest                                17
OS_alarms                                    16
loadmgm                                      16
gtm_brc                                      16
RonDetectCross                               16
gtm_dpll                                     16
stm_events                                   16
SRAM_CheckSM_MCU_pattern                     16
IMEM2_CheckSM_MCU_pattern                    16
Vsram_shared_IO                              16
gtm_tom                                      16
DMEM0_CheckSM_MCU_pattern                    16
CanMgm_calib                                 14
DigIn_calib                                  13
gtm_eisb_calib                               13
eemgm                                        12
DigIn                                        12
isb                                          12
dma                                          10
Pit_events                                    9
recovery                                      9
Pit                                           9
gtm_icm                                       8
TLE9278BQX_IOs                                8
gtm_eisb_Interface                            8
TempMgm                                       8
gtm_tbu                                       8
gtm_aru                                       8
main_c2                                       7
Adc_events                                    6
IGNLoadTest_Calib                             6
sys                                           5
buckdiagmgm                                   5
checksum                                      4
stm                                           4
OS_Hook                                       4
TempMgm_calib                                 4
mpc5500_user_init                             4
SafetyMngr_PIT                                4
vsram_checksum                                4
Flashmgm_calib                                3
WDT_wrapper                                   2
vsrammgm                                      2
RonDetectSA                                   2
ccptxdata                                     2
timing_calib                                  2
eemgm_calib                                   2
Dtc                                           1
Rli                                           1
-------------------------------------------------
total                                     93494
-----------------------------------------------------------------
-----------------------------------------------------------------
USED MEMORY SIZE FOR SRAM SECTION
SECTION                           Use[Byte]
===========================================
Section .bss            SIZE     =   31370
Section .calib          SIZE     =   16320
Section .data           SIZE     =    5284
Section .vsram          SIZE     =     143
Section .pattern_sram   SIZE     =      16
Section .ee_id0_data    SIZE     =     996
Section .ee_id1_data    SIZE     =      92
Section .ee_id2_data    SIZE     =     156
Section .ee_id3_data    SIZE     =     712
Section .ee_id4_data    SIZE     =      17
Section .ee_id5_data    SIZE     =      17
Section .ee_id6_data    SIZE     =     168
Section .ee_id7_data    SIZE     =      54
Section .ee_id8_data    SIZE     =    1099
Section .ee_id9_data    SIZE     =    4792
Section .ee_id10_data   SIZE     =       0
Section .ee_id11_data   SIZE     =       0
Section .vletext_RAM    SIZE     =    1256
=================================================================
TOTAL SIZE                       =   62492
+
Section .heap           SIZE     =      16
Section .vsram(not_used)SIZE     =     369
=================================================================
TOTAL ALLOCATED SIZE             =   62877
+
TOTAL SIZE ALIGNMENT                   323
=================================================================
TOTAL SRAM USED                       63200    (TOTAL ALLOCATED SIZE+TOTAL SIZE ALIGNMENT)
=================================================================
PERCENTAGE of SRAM USED                 96%    (TOTAL SRAM USED / SRAM SIZE)*100
-----------------------------------------------------------------


==================== Core memories use =======================
DMEM0 CORE Z4 use
MODULE                                     Use[Byte]
====================================================
Pattern1_dmem0                                     4
Pattern2_dmem0                                     4
Pattern3_dmem0                                     4
Pattern4_dmem0                                     4
cyl0_coil_d1_config                               12
cyl0_coil_d2_config                               12
cyl0_coil_d3_config                               12
cyl0_coil_d4_config                               12
cyl0_coil_d5_config                               12
cyl0_coil_d6_config                               12
cyl0_coil_d7_config                               12
cyl0_coil_d8_config                               12
cyl0_epws1_config                                 12
cyl0_epws2_config                                 12
cyl0_epws3_config                                 12
cyl0_epws4_config                                 12
cyl1_coil_d1_config                               12
cyl1_coil_d2_config                               12
cyl1_coil_d3_config                               12
cyl1_coil_d4_config                               12
cyl1_coil_d5_config                               12
cyl1_coil_d6_config                               12
cyl1_coil_d7_config                               12
cyl1_coil_d8_config                               12
cyl1_epws1_config                                 12
cyl1_epws2_config                                 12
cyl1_epws3_config                                 12
cyl1_epws4_config                                 12
cyl2_coil_d1_config                               12
cyl2_coil_d2_config                               12
cyl2_coil_d3_config                               12
cyl2_coil_d4_config                               12
cyl2_coil_d5_config                               12
cyl2_coil_d6_config                               12
cyl2_coil_d7_config                               12
cyl2_coil_d8_config                               12
cyl2_epws1_config                                 12
cyl2_epws2_config                                 12
cyl2_epws3_config                                 12
cyl2_epws4_config                                 12
cyl3_coil_d1_config                               12
cyl3_coil_d2_config                               12
cyl3_coil_d3_config                               12
cyl3_coil_d4_config                               12
cyl3_coil_d5_config                               12
cyl3_coil_d6_config                               12
cyl3_coil_d7_config                               12
cyl3_coil_d8_config                               12
cyl3_epws1_config                                 12
cyl3_epws2_config                                 12
cyl3_epws3_config                                 12
cyl3_epws4_config                                 12
cyl4_coil_d1_config                               12
cyl4_coil_d2_config                               12
cyl4_coil_d3_config                               12
cyl4_coil_d4_config                               12
cyl4_coil_d5_config                               12
cyl4_coil_d6_config                               12
cyl4_coil_d7_config                               12
cyl4_coil_d8_config                               12
cyl4_epws1_config                                 12
cyl4_epws2_config                                 12
cyl4_epws3_config                                 12
cyl4_epws4_config                                 12
cyl5_coil_d1_config                               12
cyl5_coil_d2_config                               12
cyl5_coil_d3_config                               12
cyl5_coil_d4_config                               12
cyl5_coil_d5_config                               12
cyl5_coil_d6_config                               12
cyl5_coil_d7_config                               12
cyl5_coil_d8_config                               12
cyl5_epws1_config                                 12
cyl5_epws2_config                                 12
cyl5_epws3_config                                 12
cyl5_epws4_config                                 12
cyl6_coil_d1_config                               12
cyl6_coil_d2_config                               12
cyl6_coil_d3_config                               12
cyl6_coil_d4_config                               12
cyl6_coil_d5_config                               12
cyl6_coil_d6_config                               12
cyl6_coil_d7_config                               12
cyl6_coil_d8_config                               12
cyl6_epws1_config                                 12
cyl6_epws2_config                                 12
cyl6_epws3_config                                 12
cyl6_epws4_config                                 12
cyl7_coil_d1_config                               12
cyl7_coil_d2_config                               12
cyl7_coil_d3_config                               12
cyl7_coil_d4_config                               12
cyl7_coil_d5_config                               12
cyl7_coil_d6_config                               12
cyl7_coil_d7_config                               12
cyl7_coil_d8_config                               12
cyl7_epws1_config                                 12
cyl7_epws2_config                                 12
cyl7_epws3_config                                 12
cyl7_epws4_config                                 12
eisb_config                                       36
cylinder_0_config                                116
cylinder_1_config                                116
cylinder_2_config                                116
cylinder_3_config                                116
cylinder_4_config                                116
cylinder_5_config                                116
cylinder_6_config                                116
cylinder_7_config                                116
IonBuffer                                       3200
IonBufferV                                      1600
IonBuff                                         1600
IonBuff2                                        1600
IonBuff2FFT                                      256
IonBuff2FftPeak                                  256
IonBuffFFT                                       256
IonBuffFftPeak                                   256
ion0_4_buffer                                   1600
ion1_5_buffer                                   1600
ion2_6_buffer                                   1600
ion3_7_buffer                                   1600
====================================================
TOTAL VARS						      =        17556
+
stack_core0                                     8192
====================================================
DMEM_C0                     TOTAL     =        25748

PERCENTAGE of DMEM0 USED                         39%    (TOTAL DMEM0 USED / DMEM0 TOTAL SIZE)*100
-----------------------------------------------------------------

IMEM0 CORE Z4 use
MODULE                                     Use[Byte]
====================================================
four1                                           1782
fft_real                                        1644
UpdateVtExtDiag                                  398
FillActiveRec                                     94
RecMgm_T10ms_body                               1168
IonAc_chartstep_c3_IonAcqBufMgm                 3238
IonAcqBufMgm_Task_Supervisior                    108
====================================================
IMEM_C0                     TOTAL     =         8432

PERCENTAGE of IMEM0 USED                         51%    (TOTAL IMEM0 USED / IMEM0 TOTAL SIZE)*100
-----------------------------------------------------------------

DMEM2 CORE Z2 use
MODULE                                     Use[Byte]
====================================================
====================================================
TOTAL VARS						      =            0
+
stack_core2                                     8192
====================================================
DMEM_C2                     TOTAL     =         8192

PERCENTAGE of DMEM2 USED                         16%    (TOTAL DMEM2 USED / DMEM2 TOTAL SIZE)*100
-----------------------------------------------------------------

IMEM2 CORE Z2 use
MODULE                                     Use[Byte]
====================================================
Pattern1_imem2                                     4
Pattern2_imem2                                     4
Pattern3_imem2                                     4
Pattern4_imem2                                     4
DMA_CH0_ISR                                       54
DMA_CH16_ISR                                      54
DMA_CH1_ISR                                       54
DMA_CH17_ISR                                      54
DMA_Stop                                         120
ipri_b0_saradcconf_ch4_ipri_b0_dma               156
ipri_b1_saradcconf_ch16_ipri_b1_dma              156
isec_b0_saradcconf_ch5_isec_b0_dma              2130
isec_b1_saradcconf_ch17_isec_b1_dma             2152
MSparkCmd_ISecDma                                 66
====================================================
IMEM_C2                     TOTAL     =         5012

PERCENTAGE of IMEM2 USED                         30%    (TOTAL IMEM2 USED / IMEM2 TOTAL SIZE)*100
-----------------------------------------------------------------

