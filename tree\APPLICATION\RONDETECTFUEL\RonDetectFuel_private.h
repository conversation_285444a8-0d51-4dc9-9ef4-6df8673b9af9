/*****************************************************************************************************************/
/* $HeadURL:: https://85.42.57.219/svn/Rep_GeOr/Spec_ECU/Application/RonDetectFuel/ED_001/SWE3/branches/SW_00#$  */
/* $Revision:: 262254                                                                                         $  */
/* $Date:: 2025-03-25 17:08:18 +0300 (Tue, 25 Mar 2025)                                                       $  */
/* $Author:: kucuksahinn                                                                                      $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      RonDetectFuel_private.h
 **  Date:          25-Mar-2025
 **
 **  Model Version: 1.1016
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_RonDetectFuel_private_h_
#define RTW_HEADER_RonDetectFuel_private_h_
#include "rtwtypes.h"
#include "RonDetectFuel_out.h"

/* Includes for objects with custom storage classes. */
#include "SyncMgm_out.h"
#include "RonDetectMgm_out.h"
#include "DigIn_out.h"
#include "RonDetectEst_out.h"
#include "rondetectfuel_out.h"
#include "loadmgm_out.h"
/* Includes for extra files. */
#include "CanMgmIn_out.h"
#include "DiagCanMgm_out.h"
#include "ETPU_EngineDefs.h"
#include "RonDetectEn_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
extern void Ro_RonDetectFuel_Scheduler_Init(void);
extern void RonDete_RonDetectFuel_Scheduler(int32_T controlPortIdx);

#endif                                 /* RTW_HEADER_RonDetectFuel_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_test                                                              *
 * stateflow                                                                  *
 *============================================================================*/