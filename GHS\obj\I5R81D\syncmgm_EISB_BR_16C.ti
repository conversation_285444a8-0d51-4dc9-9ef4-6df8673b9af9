cmd: cxppc -c -clear_all_lists -o obj\I5R81D\syncmgm_EISB_BR_16C.o -old_assembler :cxx_compiler.name=ecomppc :assembler.name=asppc :e_option=nokeep_comments :e_option=nono_ansi_aliasing_rules :e_option=attributes :e_option=nomultibyte_chars :e_option=nomicrosoft :e_option=nokeep_static_symbols :e_option=nosigned_char :e_option=nosigned_field :e_option=signed_enum_field :e_option=nosigned_ptr :e_option=nounique_strings :e_option=alternative_tokens :e_option=nomicrosoft_bugs :e_option=nonoidentoutput :e_option=noanalog :e_option=nognu_asm :e_option=nodata_in_declared_order :e_option=fast_malloc :e_option=allow_dollar :e_option=noarray_assignment :cx_pch_option=noautomatic :cx_pch_option=nosilent :cx_pch_option=nono_directory_match :cx_pch_option=novalidate :cx_e_option=noupgrade_cxx_errors :cx_e_option=noupgrade_c_errors :cx_e_option=nonowarn :cx_e_option=noquit_after_warnings :cx_e_option=noremark :cx_e_option=nonousebeforeset :cx_e_option=nono_for_init_warn :cx_e_option=notrigraph_warn :cx_e_option=noundefined_in_preproc_warn :cx_e_option=noshadow_variable_warn :cx_e_option=noimplicit_int_warn :cx_e_option=noformat_check :cx_e_option=msgnumbers :cx_e_option=nobriefmsgs :cx_e_option=nonowrap :cx_e_option=nofloatwarn :cx_e_option=nodoublewarn :cx_e_option=nodoubleerror :cx_e_option=nowall :cx_option=noexceptions :cx_option=nono_namespaces :cx_option=nostd_namespaces :cx_option=nono_rtti :cx_option=nonew_style_casts :cx_option=nono_bool_keyword :cx_option=nono_explicit_keyword :cx_option=nono_wchar_t_keyword :cx_option=nono_array_new_delete :cx_option=norestrict :cx_option=nono_extern_inline :cx_option=no_implicit_extern_c_type_conversion :cx_option=noc_and_cpp_functions_are_distinct :cx_option=nono_enum_overloading :cx_option=nolate_tiebreaker :cx_option=nono_readonly_virtual_tables :cx_option=nono_readonly_typeinfo :cx_option=new_outside_of_constructor :cx_option=nocommon_implicit_initialization :cx_option=nono_constructor_initialization_in_main :cx_option=noanachronisms :cx_option=noold_for_init_scoping :cx_option=nofriend_injection :cx_option=nonolinkfilter :cx_option=nolongtemps :cx_option=no__noinline_keyword :cx_option=instantiate_extern_inline :cx_option=noextract_vptrs :cx_option=notdeh :cx_option=named_rvo :cx_option=large_vtbl_offsets :cx11_option=norange_based_for :cx11_option=noauto_type :cx_template_option=nonoautomatic :cx_template_option=noimplicit :cx_template_option=distinct_signatures :cx_template_option=no_old_specializations :cx_template_option=nono_typename_keyword :cx_template_option=no_implicit_typename :cx_template_option=no_guiding_decls :cx_template_option=nononstd_qualifier_deduction :cx_template_option=nodep_name :cx_template_option=noparse_templates :cx_template_option=noexport :cx_template_option=link_once_templates :cx_template_option=nodefer_parse_function_teampltes :threadx_option=nouse_demo_library :threadx_option=noevent_logging :ose_option=noose_431 :linux_option=nohost_specific_alttoolsdir :linux_option=nopthreads :lynxos_option=nothreads :solaris2_option=nothreads :solaris2_option=nopthreads :debug_option=nofullfilename :debug_option=dbofiles :debug_option=nostackframe :debug_option=dwarf2 :debug_option=noconsistent :debug_option=nosearch_for_dba :debug_option=nodwarf2dbo :debug_option=all_types :debug_option=all_macros :debug_option=novalidate :debug_option=norelative_xof_path :debug_option=nouse_dli_files :debug_option=nolexical_constructs :debug_option=nooptimized_code :debug_option=notarget_walkable_stack :debug_option=nostabs2dbo :debug_option=noghs_library :debug_option=scan_source :debug_option=nosource_analyze_obj :debug_option=nosource_analyze_program :debug_option=noasm :debug_option=nolimits :debug_option=full_breakdots :debug_option=extend_liveness :debug_option=nokeep_if0 :debug_option=nocoverage_at_exit :debug_option=nocoverage_checksum :optimize=novector :optimize=noinline :optimize=loop :optimize=nobig :minoroptimize=peep :minoroptimize=cse :minoroptimize=constprop :minoroptimize=unroll :minoroptimize=minmax :minoroptimize=pipeline :minoroptimize=strfunc :minoroptimize=memfunc :minoroptimize=printfunc :minoroptimize=memclr :minoroptimize=tailrecursion :minoroptimize=unroll8 :minoroptimize=unrollbig :minoroptimize=autoregister :minoroptimize=overload :minoroptimize=nodelay_frame :minoroptimize=noinitializers :minoroptimize=noslowcompile :minoroptimize=pipelinesourceline :minoroptimize=peepholesourceline :minoroptimize=nolink :minoroptimize=cond :minoroptimize=explicitinline :minoroptimize=vectorpeeling :minoroptimize=standalone_align :minoroptimize=explodejumps :minoroptimize=inline_constant_math :minoripaopt=noipdeletefunctions :minoripaopt=noipconstglobals :minoripaopt=noipdeleteglobals :minoripaopt=noipconstprop :minoripaopt=noipremoveparams :minoripaopt=noiponesiteinlining :minoripaopt=noiplimitinlining :minoripaopt=noipconstantreturns :minoripaopt=noipremovereturns :minoripaopt=noipsmallinlining :minoripaopt=noipaliasreads :minoripaopt=noipaliaswrites :minoripaopt=noipaliaslibfuncs :minoripaopt=noipallocvars :minoripaopt=noiplocalize :delay=nostatic_analyze_at_link :delay=noanalyze_libraries :delay=noanalyze_shared_objects :delay=noSA_before_link :delay=noshow_static_analyzing :delay=noinf_depends :delay=noSA_without_link :delay=noSA_grammarian :delay=nodist_inf_frontend :delay=nodist_inf_local :delay=nodist_inf_dep :delay=noskip_bad_infs :delay=nosave_ind_dep :compcheck=nobounds :compcheck=noassignbound :compcheck=noassigntrunc :compcheck=nooverflowerr :compcheck=nooverflowwarn :compcheck=nousevariable :trace_option=noinmemtm :trace_option=nogen_dtrace :trace_option=nogen_dtrace2 :trace_option=nostrip_dtrace :trace_option=strip_kernel_dtrace :trace_option=notracereg :trace_option=notracereg_check :trace_option=link_tracereg_check :trace_option=nogen_fee :trace_option=nogen_fee_native :trace_option=nokernel_fee :trace_option=noknownsafedatatracepicpidusage :trace_option=nodtrace_supported :trace_option=nodtrace_implies_non_shared :trace_option=noevent_eagle :trace_option=nodtrace_asminst :trace_option=nogen_fee_arg :trace_option=nohistory_supported :asm_option=nono_warnings :asm_option=nonomacro :asm_option=noexternal :asm_option=nohas_slash_comments :asm_option=nohas_pound_comments :asm_option=nohas_weak_common :asm_option=nohas_md_option :asm_option=nouse_ease_some :asm_list_option=listing :asm_list_option=nocref :asm_list_option=nono_expand_macro :elxr_option=nono_warnings :elxr_option=nochecksum :elxr_option=noundefined_okay :elxr_option=nomultiple_okay :elxr_option=nodelete :elxr_option=noskip_layout :elxr_option=nocodefactor :elxr_option=noauto_sda :elxr_option=nolink_constprop :elxr_option=nosimilar_pools :elxr_option=notraceedge :elxr_option=nolink_trivial_inline :elxr_option=nolink_opt_address :elxr_option=noallow_different_section_types :elxr_option=noignore_debug_references :elxr_option=nounused_virtual_function_deletion :elxr_option=nodelete_call_nops :elxr_option=nooptimize_relocatable :elxr_option=noshorten_loads :elxr_option=noshorten_moves :elxr_option=noauto_wrap_funcs :elxr_option=noreject_duplicate_lib_syms :elxr_option=noallow_duplicate_ghs_syms :elxr_option=noallow_duplicate_linkonce_syms :error_option=noerror_prefix :error_option=nobasename :error_option=nofortran_style :ppc_option=constant_data_section :ppc_option=noendfunclabel :ppc_option=noppc_eieio_loads :ppc_option=noppc_eieio_stores :ppc_option=disable_altivec :ppc_option=nodisable_vrsave :ppc_option=norsc_fp_precise :ppc_option=e500_inst_fix :ppc_option=e500_inst_fix2 :ppc_option=ppc_isel :ppc_option=vle_code :ppc_option=vle_lib :ppc_option=noppc64_abi :ppc_option=noppc_bigtoc :ppc_option=disable_spe :ppc_option=nofp_full :ppc_option=noppc_fsel :ppc_option=noppc_interrupt_prologue_always_uses_stm :ppc_option=noppc_vle_carrybit_errata :ppc_option=noppc_vle_rfi_errata :ppc_option=noppc_gnu_asm_se_vle :ppc_option=noppc_convert_vle :ppc_option=noppc_vle_all_errata :ppc_option=noppc_e200_all_errata :misc_option=noflt_subnorm_is_zero :misc_option=nosubnorm_is_zero :misc_option=flt_associativity :misc_option=nono_data_in_text :misc_option=nono_zero_commons :misc_option=noversion_info :misc_option=noalign_data_sections :misc_option=noalign_sda_sections :misc_option=noalign_zda_sections :misc_option=noindividual_function_sections :misc_option=noindividual_data_sections :misc_option=noindividual_pragma_function_sections :misc_option=noindividual_pragma_data_sections :misc_option=noindividual_attribute_function_sections :misc_option=noindividual_attribute_data_sections :config_setting=noobj :config_setting=nofsoft :config_setting=nofsingle :config_setting=nofhard :config_setting=nofloatsingle :config_setting=nopic :config_setting=nopid :config_setting=nobyteorder :config_setting=longlong :config_setting=bigswitch :config_setting=nofarcalls :config_setting=nofarcall_patch :config_setting=noinline_fpfunctions :config_setting=inline_prologue :config_setting=sda :config_setting=zda :config_setting=asm3g :config_setting=noptr64 :config_setting=noprecise_signed_zero :config_setting=precise_signed_zero_compare :config_setting=nopreserve_padding_bits :config_setting=inline_volatile_struct_copy :config_setting=misalign_access :config_setting=noNaN_cmp_unordered :build_option=noone_pass :build_option=notwo_pass :build_option=nokeepcfiles :build_option=nopostpreprocess :build_option=nonoshell :build_option=nono_gen_inf :build_option=nono_write_inf :build_option=nono_normalize_pass :build_option=noadd_output_ext :driver_flags=noprelink_objects :driver_flags=nono_progname :driver_flags=lnkfile :driver_flags=noany_output_suffix :driver_flags=noignore_quietly :driver_flags=nointegrate :driver_flags=noinline_tiny :driver_flags=noprivate_defines :driver_flags=obsolete_defines :driver_flags=nodo_not_wait_for_dblink :driver_flags=noconsolidated_default_lds :driver_flags=nodont_pass_ada_obj_to_linker :driver_flags=use_ax_archiver :driver_flags=nono_drive_letter_in_cwd_in_ti :driver_flags=noskip_preincludes :driver_flags=noupdate_archive :driver_flags=nomerge_archive :driver_flags=nomerge_archive_mangle :driver_flags=switch_table :driver_flags=nofee_jmpbuf :driver_flags=nouse_mid_cpp :driver_flags=nokeepcppfiles :driver_flags=nokeepasmfiles :driver_flags=nokeeptempfiles :driver_flags=noreject_duplicates :driver_flags=noallow_cxx_duplicates :driver_flags=noallow_linkonce_duplicates :driver_flags=noforce_relative_paths :driver_flags=host64 :internal=dynamic_options :internal=noc99_strict_mode :float_flags=nono_fused_madd :checking_flags=nostack_protector :checking_flags=nostack_check :link_time_flags=nostaticlink :link_time_flags=noputversions :link_time_flags=nonostrip :link_time_flags=nonostdlib :link_time_flags=nominlib :link_time_flags=noonly_stdclib :link_time_flags=noonly_stdcxxlib :link_time_flags=norelocatable_program :link_time_flags=norelocatable_object :link_time_flags=nominimal_ghs_lib :link_time_flags=noonly_syslib :link_time_flags=nomc_only_libs_for_core :link_time_flags=nomc_only_libs_for_common :staticlink=false :minlibraries=false :passsource=true :coverage=false :showversions=false :putversions=false :ghsstdcall=false :initpipointers=true :discard_zero_initializers=false :nostdinc=false :dual_debug=true :use_vp_as_dbg_source_root=false :dwarf=true :asmsym=false :asmsymc=false :use_cpp=false :use_asm_preproc=false :preprocess_assembly=false :no_preprocess_special_assembly=false :preprocess_linker_directive=false :preprocess_linker_directive_full=false :one_instantiation_per_object=false :hybrid_one_instantiation_per_object=false :no_add_dot=true :timer_profile=false :outputmode=elf :mcs_outputfmt=c :fputype=single :ppc_cputype=ppc5744kz410 :xda_thresholdkind=normal :toolasm=ghs :wchartype=wchar_long :sourcekanji=shiftjis :targetkanji=shiftjis :packing=none :struct_min_alignment=1 :asm_mode=silent :unknown_pragma=warn :incorrect_pragma=warn :cx_mode=ansi :cx_lib=scnoe :compilation=assembly :progress=execution :debuglevel=plain :profilelevel=none :coveragelevel=none :showlevel=warnings :memcheck=none :optimizestrategy=speed :ipaoptimize=none :cx_template=whenused :cx_inline=medium :cx_enum=int :cx_virtual=standard :cx_xref_level=full :cx_link_style=linker :elxr_feemode=disable :elxr_eaglemode=no_strip :elxr_ghtwsmode=no :dc_level=none :act_like=latest :time_size=32 :large_archive=auto :gnu_version=40300 :object_dir=obj\I5R81D :cpuname=ppc5744kz410 :jmpbuf_size=256 :cx_pch_dir=obj\I5R81D :instantiation_dir=obj\I5R81D\template_dir :includefiles=I5R81D_config.h :sourcedirs=..\tree\COMMON\CONFIG\asm :sourcedirs=..\tree\COMMON\CONFIG\C :sourcedirs=..\tree\COMMON\INCLUDE :sourcedirs=..\tree\COMMON\LIB :sourcedirs=..\tree\BIOS\COMMON :sourcedirs=..\tree\AK_OSEK :sourcedirs=..\tree\DD\COMMON :sourcedirs=..\tree\APPLICATION\COMMON :sourcedirs=..\tree\EEPCOM :sourcedirs=..\common :undefines=__CWWRKS__ :defines=__ghs_board_is_ppc_generic :defines=__GHS__ :defines=__PPC_EABI__ 
dir:C:\Users\<USER>\Desktop\EISB8C_SI_03_RonDetectFuel\GHS
fnm:..\tree\DD\SYNCMGM\syncmgm_EISB_BR_16C.C
