
                                   Thu Jun 26 17:15:03 2025           Page 1
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
Command Line:   C:\ghs\comp_201516\asppc.exe -noundefined -elf -b1
                -I..\tree\BIOS\GTM\include -I..\tree\BIOS\GTM\cfg
                -I..\tree\COMMON\CONFIG\asm -I..\tree\COMMON\CONFIG\C
                -I..\tree\COMMON\INCLUDE -I..\tree\COMMON\LIB
                -I..\tree\BIOS\COMMON -I..\tree\AK_OSEK -I..\tree\DD\COMMON
                -I..\tree\APPLICATION\COMMON -I..\tree\EEPCOM -I..\common
                -IC:\ghs\comp_201516\lib\ppc5744 -cpu=ppc5744kz410 -noSPE
                -dbo=C:\Users\<USER>\Desktop\EISB8C_SI_03_RonDetectFuel\GHS\obj\I5R81D\__start_z4_GHS.dbo
                --gh_oname=obj\I5R81D\__start_z4_GHS.o
                --gh_md=obj\I5R81D\__start_z4_GHS.d -asm3g
                -asm3g_driver=C:\ghs\comp_201516\ccppc
                -asm3g_args=@@obj\I5R81D\__start_z4_GHS.a3g
                -accept_unsafe_op_names -o obj\I5R81D\__start_z4_GHS.o
                -list=obj\I5R81D\__start_z4_GHS.lst
                ..\tree\BIOS\STARTUP\__start_z4_GHS.s
Source File:    ..\tree\BIOS\STARTUP\__start_z4_GHS.s
Directory:      C:\Users\<USER>\Desktop\EISB8C_SI_03_RonDetectFuel\GHS
Host OS:        Windows
AS: Copyright (C) 1983-2015 Green Hills Software.  All Rights Reserved.
Release: Compiler v2015.1.6
Build Directory: [Directory] COMP-VAL-WIN31:l:/compiler/release-branch-2015-1-comp/build/v2015.1-2015-10-18/win32-comp-ecom
Revision: [VCInfo] http://toolsvc/branches/release-branch-70/src@543895 (built by auto-compiler)
Revision Date: Mon Oct 19 08:19:32 2015

Release Date: Mon Oct 19 11:04:55 2015

                             1	#************************************************************************/
                             2	#* FILE NAME: __start_Z4_GHS.s                                            */
                             3	#*                                                                        */
                             4	#* DESCRIPTION:                                                           */
                             5	#* This file contains functions for core 0 -Z4  assembly configuration.   */
                             6	#=========================================================================*/
                             7	.section .init_c0,"axv" // The "axv" generates symbols for debug
                             8	
                             9	#.align      2
                            10	.globl __start_c0
                            11	.type __start_c0, @function
                            12	
                            13	.extern main
                            14	.extern __SP0_INIT   
                            15	.extern __SP0_END    
                            16	.extern __STACK0_SIZE
                            17	.extern __IRAM0_START
                            18	.extern __IRAM0_END
                            19	.extern __DRAM0_START
                            20	.extern __DRAM0_END
                            21	.extern _DRAM0_INIT_START
                            22	.extern _DRAM0_INIT_SIZE 
                            23	.extern __KEYWORD1
                            24	.extern __KEYWORD2
                            25	.extern __IV_ADDR_C0
                            26	.equ      __SRAMTEST_OFFSET, 4  // number of byte offset to next data address; DO NOT MODIFY
                            27	
                            28	//BUCSR registers definitions
                            29	.equ BUCSR_BPEN,              0x00000001
                            30	.equ BUCSR_BPRED_MASK,        0x00000006
                            31	.equ BUCSR_BPRED_0,           0x00000000
                            32	.equ BUCSR_BPRED_1,           0x00000002
                            33	.equ BUCSR_BPRED_2,           0x00000004
                            34	.equ BUCSR_BPRED_3,           0x00000006
                            35	.equ BUCSR_BALLOC_MASK,       0x00000030
                            36	.equ BUCSR_BALLOC_0,          0x00000000
                            37	.equ BUCSR_BALLOC_1,          0x00000010
                            38	.equ BUCSR_BALLOC_2,          0x00000020

                                   Thu Jun 26 17:15:03 2025           Page 2
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
                            39	.equ BUCSR_BALLOC_3,          0x00000030
                            40	.equ BUCSR_BALLOC_BFI,        0x00000200
                            41	// MSR register definitions
                            42	.equ MSR_UCLE,                0x04000000
                            43	.equ MSR_SPE,                 0x02000000
                            44	.equ MSR_WE,                  0x00040000
                            45	.equ MSR_CE,                  0x00020000
                            46	.equ MSR_EE,                  0x00008000
                            47	.equ MSR_PR,                  0x00004000
                            48	.equ MSR_FP,                  0x00002000
                            49	.equ MSR_ME,                  0x00001000
                            50	.equ MSR_FE0,                 0x00000800
                            51	.equ MSR_DE,                  0x00000200
                            52	.equ MSR_FE1,                 0x00000100
                            53	.equ MSR_IS,                  0x00000020
                            54	.equ MSR_DS,                  0x00000010
                            55	.equ MSR_RI,                  0x00000002
                            56	
                            57	
                            58	// Set MSR value default (DEMO ST)
                            59	.equ MSR_DEFAULT,   (MSR_SPE | MSR_WE | MSR_CE | MSR_ME)
                            60	// Set MSR ELDOR (from EISB Andorra) 
                            61	.equ MSR_ELDOR,   (MSR_DE)
                            62	
                            63	
                            64	//Set to enable IRAM0 and DRAM0 test for core 0 (z4)
                            65	.equ RAM0_TEST, 1
                            66	
                            67	//VLE ISA section
                            68	.vle
                            69	
                            70	//**************************************************************************/
                            71	// FUNCTION     : __start_c0                                               */
                            72	// PURPOSE      : core0(z4) activation                                     */
                            73	// INPUT NOTES  :                                                          */
                            74	// RETURN NOTES : None                                                     */
                            75	// WARNING      : Registers used:                                          */
                            76	//**************************************************************************/
                            77	__start_c0:
                            78	        
                            79	    //**************************/
                            80	    //* Core0Init              */
                            81	    //**************************/
00000000 78000001           82	    e_bl cfg_sprZ4
                            83	
                            84	    //**************************/
                            85	    //* IVPR0 Init             */
                            86	    //**************************/
00000004 78000001           87	    e_bl _ivinit0
                            88	
                            89	    //*********************************************************************/ 
                            90	    //* Core0 IRAM0 and DRAM0 clearing,                                   */
                            91	    //* this device requires a write to all RAM location in               */
                            92	    //* order to initialize the ECC detection hardware, this is going to  */  
                            93	    //* slow down the startup but there is no way around.                 */
                            94	    //*********************************************************************/

                                   Thu Jun 26 17:15:03 2025           Page 3
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
00000008 78000001           95	    e_bl cfg_z4IRAM      //Write to all IRAM Core 0 locations for ECC functionality
0000000c 78000001           96	    e_bl cfg_z4DRAM      //Write to all DRAM Core 0 locations for ECC functionality 
                            97	
                            98	.if RAM0_TEST
                            99	    //*********************************************************************/
                           100	    //* Core0 IRAM0 and DRAM0 testing                                     */
                           101	    //* wriring and reading test keyword                                  */
                           102	    //*                                                                   */  
                           103	    //*********************************************************************/
00000010 78000001          104	    e_bl cfg_z4IRAMtest   // Run this function if IRAM0 test is required.
00000014 78000001          105	    e_bl cfg_z4DRAMtest   // Run this function if DRAM0 test is required.    
                           106	.endif
                           107	
                           108	    //*********************************************************************/
                           109	    //* Stack setup                                                       */
                           110	    //*********************************************************************/
00000018 78000001          111	    e_bl cfg_STACK_c0
                           112	
                           113	    //*********************************************************************/
                           114	    //* Small sections registers initialization                           */
                           115	    //*********************************************************************/
0000001c 78000001          116	    e_bl cfg_PNTRS_c0
                           117	
                           118	
                           119	    //*********************************************************************/
                           120	    //* Main program invocation                                           */
                           121	    //*********************************************************************/
00000020 78000001          122	    e_bl  main
                           123	
                           124	////////////////////////////////////////////////////////////////////////////
                           125	
                           126	
                           127	//**************************************************************************/
                           128	// FUNCTION     : cfg_sprZ4                                                */
                           129	// PURPOSE      : This function initializes to 0 the core Z4 SPRs          */
                           130	// INPUT NOTES  :                                                          */
                           131	// RETURN NOTES : None                                                     */
                           132	// WARNING      : Registers used:                                          */
                           133	//**************************************************************************/
                           134	.align      2
                           135	cfg_sprZ4:
                           136	
00000024 7c000278          137	xor    r0,  r0,  r0
00000028 7c210a78          138	xor    r1,  r1,  r1
0000002c 7c421278          139	xor    r2,  r2,  r2
00000030 7c631a78          140	xor    r3,  r3,  r3
00000034 7c842278          141	xor    r4,  r4,  r4
00000038 7ca52a78          142	xor    r5,  r5,  r5
0000003c 7cc63278          143	xor    r6,  r6,  r6
00000040 7ce73a78          144	xor    r7,  r7,  r7
00000044 7d084278          145	xor    r8,  r8,  r8
00000048 7d294a78          146	xor    r9,  r9,  r9
0000004c 7d4a5278          147	xor   r10, r10, r10
00000050 7d6b5a78          148	xor   r11, r11, r11
00000054 7d8c6278          149	xor   r12, r12, r12
00000058 7dad6a78          150	xor   r13, r13, r13

                                   Thu Jun 26 17:15:03 2025           Page 4
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
0000005c 7dce7278          151	xor   r14, r14, r14
00000060 7def7a78          152	xor   r15, r15, r15
00000064 7e108278          153	xor   r16, r16, r16
00000068 7e318a78          154	xor   r17, r17, r17
0000006c 7e529278          155	xor   r18, r18, r18
00000070 7e739a78          156	xor   r19, r19, r19
00000074 7e94a278          157	xor   r20, r20, r20
00000078 7eb5aa78          158	xor   r21, r21, r21
0000007c 7ed6b278          159	xor   r22, r22, r22
00000080 7ef7ba78          160	xor   r23, r23, r23
00000084 7f18c278          161	xor   r24, r24, r24
00000088 7f39ca78          162	xor   r25, r25, r25
0000008c 7f5ad278          163	xor   r26, r26, r26
00000090 7f7bda78          164	xor   r27, r27, r27
00000094 7f9ce278          165	xor   r28, r28, r28
00000098 7fbdea78          166	xor   r29, r29, r29
0000009c 7fdef278          167	xor   r30, r30, r30
000000a0 7ffffa78          168	xor   r31, r31, r31
                           169	
000000a4 0004              170	se_blr 
                           171	// End of cfg_sprZ4
                           172	
                           173	
                           174	//**************************************************************************/
                           175	// FUNCTION     : _ivinit0                                                 */
                           176	// PURPOSE      : Core 0 exception vectors initialization                  */
                           177	// INPUT NOTES  :                                                          */
                           178	// RETURN NOTES : None                                                     */
                           179	// WARNING      : Registers used:                                          */
                           180	//**************************************************************************/
000000a6 4400              181	.align      2
                           182	_ivinit0:
                           183	
                           184	    //MSR initialization; MC-Port from Demo
000000a8 70a0e000          185	    e_lis   r5, MSR_ELDOR@h
000000ac 70a0c200          186	    e_or2i  r5, MSR_ELDOR@l
000000b0 7ca00124          187	    mtMSR   r5
                           188	
                           189	    //IVPR initialization
000000b4 70a0e000          190	    e_lis  r5, __IV_ADDR_C0@h   // IVPR = address base (24 MS bits)
000000b8 70a0c000          191	    e_or2i r5, __IV_ADDR_C0@l   // IVPR = address base (24 MS bits)
000000bc 7cbf0ba6          192	    mtIVPR r5
                           193	   
000000c0 0004              194	se_blr
                           195	
                           196	//********************************************************************************/
                           197	// FUNCTION     : cfg_STACK                                                      */
                           198	// PURPOSE      : This function initializes a 8K Stack region.                   */
                           199	//                After the stack and the MPU                                    */
                           200	//                entries are established, C Code can be used in the             */
                           201	//                 application.                                                  */
                           202	// INPUT NOTES  : __STACK0_SIZE, __SP0_INIT, __SP0_END, (defined in linker file) */
                           203	// RETURN NOTES : None                                                           */
                           204	// WARNING      : Registers used: R1(to set stack ptr),R5                        */
                           205	//********************************************************************************/
                           206	cfg_STACK_c0:

                                   Thu Jun 26 17:15:03 2025           Page 5
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
                           207	
                           208	// Load size of Cache to be used for Stack (8K)
000000c2 70a0e000          209	    e_lis   r5, __STACK0_SIZE@h       // Stack size is 8KBytes
000000c6 70a0c000          210	    e_or2i   r5,  __STACK0_SIZE@l
                           211	// Each stack lock instruction covers 32 bytes, so divide the input parameter
000000ca 7ca52c70          212	    e_srwi  r5, r5, 5                // Shift the contents of R5 right by 5 bits (size/32)
000000ce 7ca903a6          213	    mtctr r5                       // locations per cache line loaded to the CTR (SPR 9) register
                           214	    
                           215	// Point R5 to just past the DRAM0. Set in the Linker file.   
000000d2 70a0e000          216	    e_lis   r5, __SP0_END@h    
000000d6 70a0c000          217	    e_or2i   r5, __SP0_END@l
                           218	                  
                           219	// Set the stack pointer
000000da 7020e000          220	    e_lis   r1, (__SP0_INIT-0x10)@h
000000de 703fc7f0          221	    e_or2i   r1, (__SP0_INIT-0x10)@l
                           222	
000000e2 0004              223	    se_blr
                           224	// End of cfg_STACK_c0
                           225	
                           226	//*****************************************************************************/
                           227	// FUNCTION     : cfg_PNTRS                                                   */
                           228	// PURPOSE      : This function initializes register pointers for small data  */
                           229	//                 (.sbss) in R13 and small data2 (.sdata2) in R2.            */
                           230	//                                                                            */
                           231	// INPUT NOTES  : _SDA_BASE_, _SDA2_BASE_ (defined by the linker EABI)        */
                           232	// RETURN NOTES : None                                                        */
                           233	// WARNING      : Registers used: R13(to set .sdata pointer ),                */
                           234	//                                R2 (to set .sdata2 pointer)                 */
                           235	//                 The BASE addresses are offset by 0x8000 for CW, GHS, P&E   */
                           236	//                 and offset by 0x7FF0 for Diab to simplify access to small  */
                           237	//                 data.                                                      */
                           238	//*****************************************************************************/
                           239	cfg_PNTRS_c0:
                           240	
                           241	// Set the small data (.sbss) pointer
000000e4 71a0e000          242	    e_lis   r13, (_SDA_BASE_)@h
000000e8 71a0c000          243	    e_or2i   r13, (_SDA_BASE_)@l
                           244	
                           245	// Set the small data2 (.sdata2) pointer
000000ec 7040e000          246	    e_lis   r2, (_SDA2_BASE_)@h
000000f0 7040c000          247	    e_or2i   r2, (_SDA2_BASE_)@l
                           248	
000000f4 0004              249	    se_blr
                           250	// End of cfg_PNTRS
                           251	
                           252	
                           253	//*************************************************************************/
                           254	// FUNCTION     : cfg_z4IRAM                                              */
                           255	// PURPOSE      : This function initializes the core 0 IRAM               */
                           256	//                by writing 64 bitvalues to every SRAM location.         */
                           257	//                This will set the  initial ECC (Error Correction Code). */ 
                           258	// INPUT NOTES  :                                                         */
                           259	// RETURN NOTES : None                                                    */
                           260	// WARNING      : Registers used: R5                                      */
                           261	//*************************************************************************/
                           262	cfg_z4IRAM:

                                   Thu Jun 26 17:15:03 2025           Page 6
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
                           263	
                           264	// base address of the internal z4 IRAM
000000f6 7080e000          265	    e_lis   r4,__IRAM0_START@h
000000fa 7080c000          266	    e_or2i  r4, __IRAM0_START@l
                           267	// end address of the internal z4 IRAM
000000fe 70a0e000          268	    e_lis   r5,__IRAM0_END@h
00000102 70a0c000          269	    e_or2i  r5,__IRAM0_END@l
                           270	
                           271	    iram2_loop:
00000106 7c042840          272	        cmpl cr0, r4, r5
0000010a e000              273	        se_bge      .iram2clrend
                           274	
0000010c 1a040900          275	        e_stmw  r16,0(r4)       // write all SPR registers from r16 to r31 to L2RAM
00000110 1c840040          276	        e_add16i  r4,r4,64      // increment the ram ptr
00000114 78000000          277	        e_b  iram2_loop         // loop for 64k of L2RAM
                           278	
                           279	    .iram2clrend:
00000118 0004              280	    se_blr     
                           281	// End of cfg_z4IRAM
                           282	
                           283	//*************************************************************************/
                           284	// FUNCTION     : cfg_z4DRAM                                              */
                           285	// PURPOSE      : This function initializes the core 0 DRAM               */
                           286	//                by writing 64 bitvalues to every SRAM location.        */
                           287	//                This will set the  initial ECC (Error Correction Code). */ 
                           288	// INPUT NOTES  : INT_SRAM_BASE, INT_SRAM_64BYTSEGS (INT_SRAM_SIZE >> 6) */
                           289	// RETURN NOTES : None                                                    */
                           290	// WARNING      : Registers used: R5                                      */
                           291	//*************************************************************************/
                           292	cfg_z4DRAM:
                           293	    // base address of the internal z4 DRAM
0000011a 7080e000          294	        e_lis   r4,__SP0_END@h   // __SP0_END = first DRAM address
0000011e 7080c000          295	        e_or2i  r4, __SP0_END@l
                           296	    // end address of the internal z4 DRAM
00000122 70a0e000          297	        e_lis   r5,__DRAM0_END@h
00000126 70a0c000          298	        e_or2i  r5,__DRAM0_END@l
                           299	    
                           300	        dram2_loop:
0000012a 7c042840          301	            cmpl cr0, r4, r5
0000012e e000              302	            se_bge      .dram2clrend
                           303	    
00000130 1a040900          304	            e_stmw  r16,0(r4)     // write all SPR registers from r16 to r31 to L2RAM
00000134 1c840040          305	            e_add16i  r4,r4,64    // increment the ram ptr
00000138 78000000          306	            e_b  dram2_loop       // loop for 64k of L2RAM
                           307	    
                           308	        .dram2clrend:
0000013c 0004              309	        se_blr  
                           310	
                           311	// End of cfg_z4DRAM
                           312	
                           313	.if RAM0_TEST 
                           314	//*****************************************************************************/
                           315	// FUNCTION     : cfg_z4IRAMtest                                               */
                           316	// PURPOSE      : This function                                               */
                           317	//                                                                            */
                           318	//                                                                            */

                                   Thu Jun 26 17:15:03 2025           Page 7
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
                           319	// INPUT NOTES  : This function performs IRAM4 test                           */
                           320	//                                                                            */
                           321	//                __KEYWORD1        --  defined below                         */
                           322	//                __KEYWORD2        --  defined below                         */
                           323	//                __SRAM_SIZE       --  defined below                         */
                           324	//                __SRAM_START_ADDR --  defined below                         */
                           325	//                __SRAMTEST_OFFSET -- (=4) defined below                     */
                           326	//                                                                            */
                           327	// RETURN NOTES : None                                                        */
                           328	// WARNING      : Registers used: R12 -- set SRAM pointer                     */
                           329	//                                R11 -- set SRAM pointer                     */
                           330	//                                R10 -- set SRAM pointer                     */
                           331	//                                R9  -- hold SRAM size                       */ 
                           332	//                                R7  -- loop counter working register        */
                           333	//                                R6  -- hold keywords loop counter threshold */
                           334	//                                R5  -- hold actual keyword to test          */
                           335	//                                R4  --    hold the SRAM copy data           */                          
                           336	//                                R3  -- 2nd loop counter working register    */
                           337	//*****************************************************************************/
                           338	cfg_z4IRAMtest:
                           339	    // CHECK  __SRAM_SIZE1
                           340	
0000013e 7ce802a6          341	        mfLR  r7
                           342	    // Set GPR9 to the count of the RAM load size
                           343	        
00000142 7120e000          344	        e_lis    r9, __IRAM0_SIZE@ha          // Load upper SRAM load size (// of bytes) into R9
00000146 70098800          345	        e_add2i. r9, __IRAM0_SIZE@l           // Load lower SRAM load size into R9
                           346	                                              //  The "." sets the condition flag
0000014a 7a120000          347	        e_beq _func_exit                      // Exit cfg_SRAMtest if size is zero
                           348	
                           349	        
0000014e 70a0e000          350	        e_lis   r5, __KEYWORD1@ha             // Load upper address of first Keyword into R5
00000152 1ca50000          351	        e_add16i  r5,r5, __KEYWORD1@l         // Load lower address of first Keyword into R5
                           352	
00000156 7140e000          353	        e_lis   r10, __IRAM0_START@ha         // Load address of first SRAM load into R10
0000015a 1d4a0000          354	        e_add16i  r10,r10, __IRAM0_START@l    // Load lower address of SRAM load into R10
0000015e 1d4afffc          355	        e_add16i  r10,r10, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    
                           356	
00000162 7d4b5378          357	        mr r11,r10
                           358	        
00000166 78000001          359	        e_bl SramCheckFunction
                           360	        
0000016a 70a0e000          361	        e_lis   r5, __KEYWORD2@ha             // Load upper address of first Keyword into R5
0000016e 1ca50000          362	        e_add16i  r5,r5, __KEYWORD2@l         // Load lower address of first Keyword into R5
                           363	
00000172 7140e000          364	        e_lis   r10, __IRAM0_START@ha         // Load address of first SRAM load into R10
00000176 1d4a0000          365	        e_add16i  r10,r10, __IRAM0_START@l    // Load lower address of SRAM load into R10
0000017a 1d4afffc          366	        e_add16i  r10,r10, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    
                           367	
0000017e 7d4b5378          368	        mr r11,r10
                           369	
00000182 78000001          370	        e_bl SramCheckFunction
                           371	
00000186 7180e000          372	        e_lis   r12, __IRAM0_START@ha         // Load address of first SRAM load into R10
0000018a 1d8c0000          373	        e_add16i  r12,r12, __IRAM0_START@l    // Load lower address of SRAM load into R10
0000018e 1d8cfffc          374	        e_add16i  r12,r12, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    

                                   Thu Jun 26 17:15:03 2025           Page 8
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
                           375	
00000192 78000001          376	        e_bl SramClearMemory
                           377	
                           378	    _func_exit:
00000196 7ce803a6          379	        mtLR  r7
0000019a 0004              380	        se_blr
                           381	
                           382	// End of cfg_z4IRAMtest
                           383	
                           384	
                           385	//*****************************************************************************/
                           386	// FUNCTION     : cfg_z4DRAMtest                                              */
                           387	// PURPOSE      : This function                                               */
                           388	//                                                                            */
                           389	//                                                                            */
                           390	// INPUT NOTES  : This function performs IRAM0 test                           */
                           391	//                                                                            */
                           392	//                __KEYWORD1        --  defined below                         */
                           393	//                __KEYWORD2        --  defined below                         */
                           394	//                __SRAM_SIZE       --  defined below                         */
                           395	//                __SRAM_START_ADDR --  defined below                         */
                           396	//                __SRAMTEST_OFFSET -- (=4) defined below                     */
                           397	//                                                                            */
                           398	// RETURN NOTES : None                                                        */
                           399	// WARNING      : Registers used: R12 -- set SRAM pointer                     */
                           400	//                                R11 -- set SRAM pointer                     */
                           401	//                                R10 -- set SRAM pointer                     */
                           402	//                                R9  -- hold SRAM size                       */ 
                           403	//                                R7  -- loop counter working register        */
                           404	//                                R6  -- hold keywords loop counter threshold */
                           405	//                                R5  -- hold actual keyword to test          */
                           406	//                                R4  --    hold the SRAM copy data           */                          
                           407	//                                R3  -- 2nd loop counter working register    */
                           408	//*****************************************************************************/
                           409	cfg_z4DRAMtest:
                           410	
0000019c 7ce802a6          411	    mfLR  r7
                           412	// Set GPR9 to the count of the RAM load size
                           413	    
000001a0 7120e000          414	    e_lis    r9, _DRAM0_INIT_SIZE@ha       // Load upper SRAM load size (// of bytes) into R9
000001a4 70098800          415	    e_add2i. r9, _DRAM0_INIT_SIZE@l        // Load lower SRAM load size into R9
                           416	                                           //  The "." sets the condition flag
000001a8 7a120000          417	    e_beq _func_exit                       // Exit cfg_SRAMtest if size is zero
                           418	
                           419	    
000001ac 70a0e000          420	    e_lis   r5, __KEYWORD1@ha              // Load upper address of first Keyword into R5
000001b0 1ca50000          421	    e_add16i  r5,r5, __KEYWORD1@l          // Load lower address of first Keyword into R5
                           422	
000001b4 7140e000          423	    e_lis   r10, _DRAM0_INIT_START@ha      // Load address of first SRAM load into R10
000001b8 1d4a0000          424	    e_add16i  r10,r10, _DRAM0_INIT_START@l // Load lower address of SRAM load into R10
000001bc 1d4afffc          425	    e_add16i  r10, r10, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    
                           426	
000001c0 7d4b5378          427	    mr r11,r10
                           428	    
000001c4 78000001          429	    e_bl SramCheckFunction
                           430	    

                                   Thu Jun 26 17:15:03 2025           Page 9
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
000001c8 70a0e000          431	    e_lis   r5, __KEYWORD2@ha              // Load upper address of first Keyword into R5
000001cc 1ca50000          432	    e_add16i  r5,r5, __KEYWORD2@l          // Load lower address of first Keyword into R5
                           433	
000001d0 7140e000          434	    e_lis   r10, _DRAM0_INIT_START@ha      // Load address of first SRAM load into R10
000001d4 1d4a0000          435	    e_add16i  r10,r10, _DRAM0_INIT_START@l // Load lower address of SRAM load into R10
000001d8 1d4afffc          436	    e_add16i  r10,r10, -__SRAMTEST_OFFSET  // Decrement address to prepare for _writeKeyLoop    
                           437	
000001dc 7d4b5378          438	    mr r11,r10
                           439	
000001e0 78000001          440	    e_bl SramCheckFunction
                           441	
000001e4 7180e000          442	    e_lis   r12, _DRAM0_INIT_START@ha      // Load address of first SRAM load into R10
000001e8 1d8c0000          443	    e_add16i  r12,r12, _DRAM0_INIT_START@l // Load lower address of SRAM load into R10
000001ec 1d8cfffc          444	    e_add16i  r12,r12, -__SRAMTEST_OFFSET  // Decrement address to prepare for _writeKeyLoop    
                           445	
000001f0 78000001          446	    e_bl SramClearMemory
                           447	
                           448	_func_exit_1:
000001f4 7ce803a6          449	    mtLR  r7
000001f8 0004              450	    se_blr
                           451	
                           452	
                           453	// End of cfg_SRAMtest
                           454	
                           455	//**********************************************************************
                           456	//                  CHECK FUNCTION:SramCheckFunction
                           457	//
                           458	//
                           459	//**********************************************************************
                           460	SramCheckFunction:
                           461	
000001fa 7d2903a6          462	    mtctr  r9                            // Store number of bytes to be moved in spr CTR 
                           463	
                           464	_writeKeyLoop:
000001fe 18aa0604          465	    e_stwu   r5, __SRAMTEST_OFFSET(r10)  // Store R5 data word into SRAM at R10 and update SRAM address 
00000202 7a200000          466	    e_bdnz   _writeKeyLoop               // Branch if more bytes to load from SRAM
                           467	
00000206 7d6a5b78          468	    mr r10,r11
0000020a 7d2903a6          469	    mtctr   r9                           // Store number of bytes to be moved in spr CTR
                           470	
                           471	
                           472	_verifyKeyLoop:
0000020e 188a0204          473	    e_lwzu   r4, __SRAMTEST_OFFSET(r10)  // Load data word at R4 into R10,incrementing (update) SRAM address
00000212 7c052040          474	    cmplw  r5,r4                         // Check if stored word is equal to test keyword
00000216 7a020000          475	    e_bne    _error                      // if not jump to SRAM test failure section
0000021a 7a200000          476	    e_bdnz   _verifyKeyLoop              // Branch if more bytes to load from SRAM
0000021e 78000000          477	    e_b _endloop
                           478	
                           479	_error:
00000222 1800d000          480	    nop
                           481	    //e_bl SYS_SRAM_Test_FailureRoutine  MC, da valutare in BT_MV
00000226 78000001          482	    e_bl _error  //MC, loop
                           483	_endloop:    
0000022a 0004              484	    se_blr
                           485	
                           486	

                                   Thu Jun 26 17:15:03 2025           Page 10
                                   Fri May 23 11:35:03 2025   __start_z4_GHS.s
                           487	// End of SramCheckFunction
                           488	
                           489	
                           490	//**********************************************************************
                           491	//                  CHECK FUNCTION:SramClearMemory
                           492	//
                           493	//
                           494	//**********************************************************************
                           495	
                           496	SramClearMemory:
                           497	        
0000022c 70a0e000          498	    e_lis   r5, __CLEAR_KEYWORD@ha       // Load upper address of Clear Keyword into R5
00000230 1ca50000          499	    e_add16i  r5,r5, __CLEAR_KEYWORD@l   // Load lower address of Clear Keyword into R5
                           500	    
00000234 7d2903a6          501	    mtctr r9                             // Store of bytes to be moved in spr CTR
                           502	
                           503	_ClearMemoryLoop:
00000238 18ac0604          504	    e_stwu   r5, __SRAMTEST_OFFSET(r12)  // Store R5 data word into SRAM at R12 and update SRAM address 
0000023c 7a200000          505	    e_bdnz   _ClearMemoryLoop            // Branch if more bytes to load from ROM
                           506	//end _ClearMemoryLoop
                           507	
00000240 0004              508	se_blr
                           509	
                           510	
                           511	// End of SramClearMemory
                           512	.endif
                           513	
